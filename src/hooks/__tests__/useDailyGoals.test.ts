/**
 * Tests for useDailyGoals hook
 */

import { renderHook, act } from '@testing-library/react';
import { useDailyGoals } from '../useDailyGoals';
import { DailyGoal, DailyGoalAchievement } from '../../types/goal';

// Mock useLocalStorage
const mockSetCurrentGoal = jest.fn();
const mockSetAchievements = jest.fn();
const mockUseLocalStorage = jest.fn();

jest.mock('../useLocalStorage', () => ({
  useLocalStorage: jest.fn(),
}));

// Mock constants
jest.mock('../../constants', () => ({
  STORAGE_KEYS: {
    DAILY_GOAL: 'dailyGoal',
    DAILY_GOAL_ACHIEVEMENTS: 'dailyGoalAchievements',
  },
}));

// Mock Date to avoid conflicts with test setup
const mockDate = new Date('2024-01-02T12:00:00.000Z');
const originalDate = global.Date;

beforeAll(() => {
  global.Date = jest.fn(() => mockDate) as any;
  global.Date.now = jest.fn(() => mockDate.getTime());
  global.Date.toISOString = jest.fn(() => mockDate.toISOString());
});

afterAll(() => {
  global.Date = originalDate;
});

describe('useDailyGoals', () => {
  const mockGoal: DailyGoal = {
    targetAmount: 100,
    currency: 'USD',
    isEnabled: true,
    lastNotifiedPercent: 0,
    lastNotificationDate: '2024-01-01',
    createdAt: '2024-01-01T10:00:00.000Z',
    updatedAt: '2024-01-01T10:00:00.000Z',
  };

  const mockAchievement: DailyGoalAchievement = {
    id: 'achievement_1',
    date: '2024-01-01',
    goalAmount: 100,
    earnedAmount: 120,
    currency: 'USD',
    percentageAchieved: 120,
    status: 'exceeded',
    difference: 20,
    recordedAt: '2024-01-01T23:59:59.000Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset the mock implementation
    const { useLocalStorage } = require('../useLocalStorage');
    useLocalStorage.mockImplementation(mockUseLocalStorage);

    // Mock Math.random for consistent IDs
    jest.spyOn(Math, 'random').mockReturnValue(0.123456789);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with null goal and empty achievements', () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      expect(result.current.currentGoal).toBe(null);
      expect(result.current.achievements).toEqual([]);
    });

    it('should call useLocalStorage with correct parameters', () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      renderHook(() => useDailyGoals());

      expect(mockUseLocalStorage).toHaveBeenCalledWith('dailyGoal', null);
      expect(mockUseLocalStorage).toHaveBeenCalledWith('dailyGoalAchievements', []);
    });

    it('should initialize with existing goal and achievements', () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[mockAchievement], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      expect(result.current.currentGoal).toEqual(mockGoal);
      expect(result.current.achievements).toEqual([mockAchievement]);
    });
  });

  describe('getCurrentGoal', () => {
    it('should return null when no goal exists', () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      expect(result.current.getCurrentGoal()).toBe(null);
    });

    it('should return current goal when it exists', () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      expect(result.current.getCurrentGoal()).toEqual(mockGoal);
    });
  });

  describe('updateDailyGoal', () => {

    it('should create new goal when none exists', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateDailyGoal({
          targetAmount: 150,
          currency: 'EUR',
          isEnabled: true,
        });
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        targetAmount: 150,
        currency: 'EUR',
        isEnabled: true,
        lastNotifiedPercent: 0,
        lastNotificationDate: '',
        createdAt: '2024-01-02T12:00:00.000Z',
        updatedAt: '2024-01-02T12:00:00.000Z',
      });
    });

    it('should update existing goal', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateDailyGoal({
          targetAmount: 200,
          currency: 'EUR',
        });
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        ...mockGoal,
        targetAmount: 200,
        currency: 'EUR',
        updatedAt: '2024-01-02T12:00:00.000Z',
      });
    });

    it('should preserve existing timestamps when updating', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateDailyGoal({
          targetAmount: 200,
        });
      });

      const updatedGoal = mockSetCurrentGoal.mock.calls[0][0];
      expect(updatedGoal.createdAt).toBe(mockGoal.createdAt);
      expect(updatedGoal.lastNotifiedPercent).toBe(mockGoal.lastNotifiedPercent);
      expect(updatedGoal.lastNotificationDate).toBe(mockGoal.lastNotificationDate);
    });
  });

  describe('enableDailyGoal', () => {
    beforeEach(() => {
      const mockDate = new Date('2024-01-02T12:00:00.000Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
    });

    it('should enable existing goal', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([{ ...mockGoal, isEnabled: false }, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.enableDailyGoal(true);
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        ...mockGoal,
        isEnabled: true,
        updatedAt: '2024-01-02T12:00:00.000Z',
      });
    });

    it('should disable existing goal', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.enableDailyGoal(false);
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        ...mockGoal,
        isEnabled: false,
        updatedAt: '2024-01-02T12:00:00.000Z',
      });
    });

    it('should create new goal when enabling and no goal exists', async () => {
      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.enableDailyGoal(true);
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        targetAmount: 0,
        currency: 'USD',
        isEnabled: true,
        lastNotifiedPercent: 0,
        lastNotificationDate: '',
        createdAt: '2024-01-02T12:00:00.000Z',
        updatedAt: '2024-01-02T12:00:00.000Z',
      });
    });

    it('should not create goal when disabling and no goal exists', async () => {
      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.enableDailyGoal(false);
      });

      expect(mockSetCurrentGoal).not.toHaveBeenCalled();
    });
  });

  describe('getAchievementsForDateRange', () => {
    const achievements = [
      { ...mockAchievement, id: 'ach1', date: '2024-01-01' },
      { ...mockAchievement, id: 'ach2', date: '2024-01-02' },
      { ...mockAchievement, id: 'ach3', date: '2024-01-03' },
      { ...mockAchievement, id: 'ach4', date: '2024-01-05' },
    ];

    it('should return achievements within date range', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([achievements, mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      let filteredAchievements: DailyGoalAchievement[];
      await act(async () => {
        filteredAchievements = await result.current.getAchievementsForDateRange(
          '2024-01-02',
          '2024-01-04'
        );
      });

      expect(filteredAchievements!).toHaveLength(2);
      expect(filteredAchievements!.map(a => a.id)).toEqual(['ach2', 'ach3']);
    });

    it('should return empty array when no achievements in range', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([achievements, mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      let filteredAchievements: DailyGoalAchievement[];
      await act(async () => {
        filteredAchievements = await result.current.getAchievementsForDateRange(
          '2024-01-10',
          '2024-01-15'
        );
      });

      expect(filteredAchievements!).toHaveLength(0);
    });

    it('should include boundary dates', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([achievements, mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      let filteredAchievements: DailyGoalAchievement[];
      await act(async () => {
        filteredAchievements = await result.current.getAchievementsForDateRange(
          '2024-01-01',
          '2024-01-03'
        );
      });

      expect(filteredAchievements!).toHaveLength(3);
      expect(filteredAchievements!.map(a => a.id)).toEqual(['ach1', 'ach2', 'ach3']);
    });
  });

  describe('recordAchievement', () => {
    beforeEach(() => {
      const mockDate = new Date('2024-01-02T23:59:59.000Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
    });

    it('should record new achievement', async () => {
      const existingAchievements = [mockAchievement];
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([existingAchievements, mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      const achievementData = {
        date: '2024-01-02',
        goalAmount: 150,
        earnedAmount: 180,
        currency: 'USD',
        percentageAchieved: 120,
        status: 'exceeded' as const,
        difference: 30,
      };

      let newAchievement: DailyGoalAchievement;
      await act(async () => {
        newAchievement = await result.current.recordAchievement(achievementData);
      });

      expect(newAchievement!).toEqual({
        ...achievementData,
        id: 'achievement_1640995200000_4fzyo82',
        recordedAt: '2024-01-02T23:59:59.000Z',
      });

      expect(mockSetAchievements).toHaveBeenCalledWith([
        ...existingAchievements,
        newAchievement!,
      ]);
    });

    it('should generate unique IDs for multiple achievements', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      // Mock different random values for unique IDs
      jest.spyOn(Math, 'random')
        .mockReturnValueOnce(0.123456789)
        .mockReturnValueOnce(0.987654321);

      const { result } = renderHook(() => useDailyGoals());

      const achievementData1 = {
        date: '2024-01-01',
        goalAmount: 100,
        earnedAmount: 100,
        currency: 'USD',
        percentageAchieved: 100,
        status: 'hit' as const,
        difference: 0,
      };

      const achievementData2 = {
        date: '2024-01-02',
        goalAmount: 100,
        earnedAmount: 80,
        currency: 'USD',
        percentageAchieved: 80,
        status: 'missed' as const,
        difference: -20,
      };

      let achievement1: DailyGoalAchievement;
      let achievement2: DailyGoalAchievement;

      await act(async () => {
        achievement1 = await result.current.recordAchievement(achievementData1);
      });

      await act(async () => {
        achievement2 = await result.current.recordAchievement(achievementData2);
      });

      expect(achievement1!.id).toBe('achievement_1640995200000_4fzyo82');
      expect(achievement2!.id).toBe('achievement_1640995200000_zik0nal');
      expect(achievement1!.id).not.toBe(achievement2!.id);
    });

    it('should handle different achievement statuses', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      const testCases = [
        {
          status: 'hit' as const,
          earnedAmount: 100,
          difference: 0,
          percentageAchieved: 100,
        },
        {
          status: 'missed' as const,
          earnedAmount: 75,
          difference: -25,
          percentageAchieved: 75,
        },
        {
          status: 'exceeded' as const,
          earnedAmount: 125,
          difference: 25,
          percentageAchieved: 125,
        },
      ];

      for (const testCase of testCases) {
        const achievementData = {
          date: '2024-01-01',
          goalAmount: 100,
          earnedAmount: testCase.earnedAmount,
          currency: 'USD',
          percentageAchieved: testCase.percentageAchieved,
          status: testCase.status,
          difference: testCase.difference,
        };

        let achievement: DailyGoalAchievement;
        await act(async () => {
          achievement = await result.current.recordAchievement(achievementData);
        });

        expect(achievement!.status).toBe(testCase.status);
        expect(achievement!.earnedAmount).toBe(testCase.earnedAmount);
        expect(achievement!.difference).toBe(testCase.difference);
        expect(achievement!.percentageAchieved).toBe(testCase.percentageAchieved);
      }
    });
  });

  describe('updateNotificationState', () => {
    beforeEach(() => {
      const mockDate = new Date('2024-01-02T15:30:00.000Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
    });

    it('should update notification state for existing goal', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateNotificationState(75, '2024-01-02');
      });

      expect(mockSetCurrentGoal).toHaveBeenCalledWith({
        ...mockGoal,
        lastNotifiedPercent: 75,
        lastNotificationDate: '2024-01-02',
        updatedAt: '2024-01-02T15:30:00.000Z',
      });
    });

    it('should not update when no goal exists', async () => {
      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateNotificationState(50, '2024-01-02');
      });

      expect(mockSetCurrentGoal).not.toHaveBeenCalled();
    });

    it('should handle different notification percentages', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      const testPercentages = [0, 50, 75, 100, 150];

      for (const percent of testPercentages) {
        await act(async () => {
          await result.current.updateNotificationState(percent, '2024-01-02');
        });

        expect(mockSetCurrentGoal).toHaveBeenCalledWith(
          expect.objectContaining({
            lastNotifiedPercent: percent,
            lastNotificationDate: '2024-01-02',
          })
        );
      }
    });
  });

  describe('return values', () => {
    it('should return all expected properties', () => {
      const { result } = renderHook(() => useDailyGoals());

      // State
      expect(result.current).toHaveProperty('currentGoal');
      expect(result.current).toHaveProperty('achievements');

      // Goal management
      expect(result.current).toHaveProperty('getCurrentGoal');
      expect(result.current).toHaveProperty('updateDailyGoal');
      expect(result.current).toHaveProperty('enableDailyGoal');

      // Achievement management
      expect(result.current).toHaveProperty('getAchievementsForDateRange');
      expect(result.current).toHaveProperty('recordAchievement');

      // Notification management
      expect(result.current).toHaveProperty('updateNotificationState');
    });

    it('should return functions for all methods', () => {
      const { result } = renderHook(() => useDailyGoals());

      expect(typeof result.current.getCurrentGoal).toBe('function');
      expect(typeof result.current.updateDailyGoal).toBe('function');
      expect(typeof result.current.enableDailyGoal).toBe('function');
      expect(typeof result.current.getAchievementsForDateRange).toBe('function');
      expect(typeof result.current.recordAchievement).toBe('function');
      expect(typeof result.current.updateNotificationState).toBe('function');
    });
  });

  describe('edge cases', () => {
    it('should handle empty achievements array', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      let achievements: DailyGoalAchievement[];
      await act(async () => {
        achievements = await result.current.getAchievementsForDateRange(
          '2024-01-01',
          '2024-01-31'
        );
      });

      expect(achievements!).toEqual([]);
    });

    it('should handle goal updates with partial data', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([mockGoal, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      await act(async () => {
        await result.current.updateDailyGoal({ targetAmount: 250 });
      });

      const updatedGoal = mockSetCurrentGoal.mock.calls[0][0];
      expect(updatedGoal.targetAmount).toBe(250);
      expect(updatedGoal.currency).toBe(mockGoal.currency); // Should preserve existing values
      expect(updatedGoal.isEnabled).toBe(mockGoal.isEnabled);
    });

    it('should handle achievement recording with minimal data', async () => {
      mockUseLocalStorage
        .mockReturnValueOnce([null, mockSetCurrentGoal])
        .mockReturnValueOnce([[], mockSetAchievements]);

      const { result } = renderHook(() => useDailyGoals());

      const minimalAchievement = {
        date: '2024-01-01',
        goalAmount: 0,
        earnedAmount: 0,
        currency: 'USD',
        percentageAchieved: 0,
        status: 'missed' as const,
        difference: 0,
      };

      let achievement: DailyGoalAchievement;
      await act(async () => {
        achievement = await result.current.recordAchievement(minimalAchievement);
      });

      expect(achievement!).toEqual(
        expect.objectContaining({
          ...minimalAchievement,
          id: expect.any(String),
          recordedAt: expect.any(String),
        })
      );
    });
  });
});
